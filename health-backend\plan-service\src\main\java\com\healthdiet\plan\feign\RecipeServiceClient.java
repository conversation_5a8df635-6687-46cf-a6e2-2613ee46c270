package com.healthdiet.plan.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 菜谱服务Feign客户端
 */
@FeignClient(name = "recipe-service", url = "http://localhost:8083", path = "/recipes")
public interface RecipeServiceClient {

    /**
     * 获取菜谱详情
     */
    @GetMapping("/{id}")
    Result<RecipeInfo> getRecipeByIdInternal(@PathVariable("id") Long id);

    /**
     * 获取菜谱食材列表
     */
    @GetMapping("/{id}/ingredients")
    Result<java.util.List<RecipeIngredient>> getRecipeIngredientsInternal(@PathVariable("id") Long id);

    /**
     * 获取菜谱详情的适配方法
     */
    default RecipeInfo getRecipeById(Long id) {
        Result<RecipeInfo> result = getRecipeByIdInternal(id);
        if (result.getCode() == 200) {
            return result.getData();
        } else {
            throw new RuntimeException("获取菜谱信息失败: " + result.getMessage());
        }
    }

    /**
     * 获取菜谱食材的适配方法
     */
    default java.util.List<RecipeIngredient> getRecipeIngredients(Long id) {
        Result<java.util.List<RecipeIngredient>> result = getRecipeIngredientsInternal(id);
        if (result.getCode() == 200) {
            return result.getData();
        } else {
            throw new RuntimeException("获取菜谱食材失败: " + result.getMessage());
        }
    }
    
    /**
     * 菜谱信息DTO
     */
    class RecipeInfo {
        private Long id;
        private String title;
        private String coverImageUrl;
        private Integer estimatedCalories;
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getTitle() { return title; }
        public void setTitle(String title) { this.title = title; }
        
        public String getCoverImageUrl() { return coverImageUrl; }
        public void setCoverImageUrl(String coverImageUrl) { this.coverImageUrl = coverImageUrl; }
        
        public Integer getEstimatedCalories() { return estimatedCalories; }
        public void setEstimatedCalories(Integer estimatedCalories) { this.estimatedCalories = estimatedCalories; }
    }
    
    /**
     * 菜谱食材DTO
     */
    class RecipeIngredient {
        private String ingredientName;
        private String quantity;
        
        // Getters and Setters
        public String getIngredientName() { return ingredientName; }
        public void setIngredientName(String ingredientName) { this.ingredientName = ingredientName; }
        
        public String getQuantity() { return quantity; }
        public void setQuantity(String quantity) { this.quantity = quantity; }
    }

    /**
     * 通用结果包装类
     */
    class Result<T> {
        private Integer code;
        private String message;
        private T data;

        // Getters and Setters
        public Integer getCode() { return code; }
        public void setCode(Integer code) { this.code = code; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
    }
}
