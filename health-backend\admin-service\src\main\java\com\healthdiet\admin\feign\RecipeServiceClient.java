package com.healthdiet.admin.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 菜谱服务Feign客户端
 */
@FeignClient(name = "recipe-service", url = "http://localhost:8083")
public interface RecipeServiceClient {
    
    /**
     * 获取菜谱总数
     */
    @GetMapping("/api/recipes/count")
    Result<Long> getRecipeCount();
    
    /**
     * 获取今日新增菜谱数
     */
    @GetMapping("/api/recipes/count/today")
    Result<Long> getTodayNewRecipeCount();
    
    /**
     * 获取AI生成菜谱数
     */
    @GetMapping("/api/recipes/count/ai")
    Result<Long> getAiRecipeCount();
    
    /**
     * 获取用户创建菜谱数
     */
    @GetMapping("/api/recipes/count/user")
    Result<Long> getUserRecipeCount();
    
    /**
     * 获取收藏总数
     */
    @GetMapping("/api/recipes/favorites/count")
    Result<Long> getFavoriteCount();
    
    /**
     * 获取今日新增收藏数
     */
    @GetMapping("/api/recipes/favorites/count/today")
    Result<Long> getTodayNewFavoriteCount();
    
    /**
     * 通用结果包装类
     */
    class Result<T> {
        private Integer code;
        private String message;
        private T data;
        
        // Getters and Setters
        public Integer getCode() { return code; }
        public void setCode(Integer code) { this.code = code; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
    }
}
