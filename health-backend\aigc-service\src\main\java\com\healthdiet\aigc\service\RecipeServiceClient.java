package com.healthdiet.aigc.service;

import com.healthdiet.aigc.dto.AiRecipeData;
import com.healthdiet.common.result.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 菜谱服务客户端
 */
@FeignClient(name = "recipe-service", path = "/recipes")
public interface RecipeServiceClient {

    /**
     * 创建AI生成的菜谱
     */
    @PostMapping("/ai")
    Result<Long> createAiRecipeInternal(
            @RequestHeader("X-User-Id") Long userId,
            @RequestBody AiRecipeData recipeData
    );

    /**
     * 创建AI菜谱的适配方法
     */
    default Long createAiRecipe(Long userId, AiRecipeData recipeData) {
        Result<Long> result = createAiRecipeInternal(userId, recipeData);
        if (result.getCode() == 200) {
            return result.getData();
        } else {
            throw new RuntimeException("创建AI菜谱失败: " + result.getMessage());
        }
    }
}
