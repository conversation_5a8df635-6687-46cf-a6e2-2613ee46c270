server:
  port: 8085

spring:
  application:
    name: aigc-service
  
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
      timeout: 10000ms
      lettuce:
        pool:
          max-active: 8
          max-wait: -1ms
          max-idle: 8
          min-idle: 0
  
  # RabbitMQ配置
  rabbitmq:
    host: localhost
    port: 5672
    username: guest
    password: guest
    virtual-host: /
    listener:
      simple:
        retry:
          enabled: true
          max-attempts: 3
          initial-interval: 1000ms
  
  # Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        enabled: false  # 测试时禁用Nacos
      config:
        server-addr: localhost:8848
        file-extension: yml
        enabled: false  # 测试时禁用Nacos

# DeepSeek API配置
deepseek:
  api:
    key: ***********************************
    base-url: https://api.deepseek.com/v1
    model: deepseek-chat
    timeout: 30000ms
    max-tokens: 2000

# RabbitMQ队列配置
rabbitmq:
  queues:
    recipe-generation: recipe.generation.queue

# 日志配置
logging:
  level:
    com.healthdiet.aigc: debug
    org.springframework.amqp: debug

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
