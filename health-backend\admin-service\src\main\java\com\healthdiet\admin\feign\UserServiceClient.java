package com.healthdiet.admin.feign;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 用户服务Feign客户端
 */
@FeignClient(name = "user-service", url = "http://localhost:8082")
public interface UserServiceClient {
    
    /**
     * 获取用户总数
     */
    @GetMapping("/api/users/count")
    Result<Long> getUserCount();
    
    /**
     * 获取今日新增用户数
     */
    @GetMapping("/api/users/count/today")
    Result<Long> getTodayNewUserCount();
    
    /**
     * 通用结果包装类
     */
    class Result<T> {
        private Integer code;
        private String message;
        private T data;
        
        // Getters and Setters
        public Integer getCode() { return code; }
        public void setCode(Integer code) { this.code = code; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
    }
}
