package com.healthdiet.user.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户健康档案实体类
 */
@Data
@TableName("user_health_profiles")
public class UserHealthProfile {
    
    @TableId(type = IdType.INPUT)
    private Long userId;
    
    /**
     * 性别：0-女, 1-男
     */
    private Integer gender;
    
    /**
     * 年龄
     */
    private Integer age;
    
    /**
     * 身高(cm)
     */
    private BigDecimal height;
    
    /**
     * 体重(kg)
     */
    private BigDecimal weight;
    
    /**
     * 运动频率：1-基本不动, 2-每周1-3次, 3-每周3-5次, 4-每周6-7次
     */
    private Integer activityLevel;
    
    /**
     * 健康目标：1-减脂, 2-保持体重, 3-增肌
     */
    private Integer goal;
    
    /**
     * 每日推荐卡路里
     */
    private Integer targetCalories;
    
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdAt;
    
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
