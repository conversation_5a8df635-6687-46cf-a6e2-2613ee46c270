server:
  port: 8080

spring:
  application:
    name: gateway-service
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        enabled: true
      config:
        server-addr: localhost:8848
        file-extension: yml
        enabled: true
    gateway:
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
      routes:
        # 认证服务路由
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/api/auth/**
          filters:
            - StripPrefix=2

        # 用户服务路由
        - id: user-service
          uri: lb://user-service
          predicates:
            - Path=/api/users/**
          filters:
            - StripPrefix=2
        
        # 菜谱服务路由
        - id: recipe-service
          uri: lb://recipe-service
          predicates:
            - Path=/api/recipes/**
          filters:
            - StripPrefix=2

        # AIGC服务路由
        - id: aigc-service
          uri: lb://aigc-service
          predicates:
            - Path=/api/aigc/**
          filters:
            - StripPrefix=2

        # 计划服务路由
        - id: plan-service
          uri: lb://plan-service
          predicates:
            - Path=/api/plans/**
          filters:
            - StripPrefix=2

        # 管理服务路由
        - id: admin-service
          uri: lb://admin-service
          predicates:
            - Path=/api/admin/**
          filters:
            - StripPrefix=2
      
      # 全局CORS配置
      globalcors:
        cors-configurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders: "*"
            allowCredentials: true

# Sentinel配置
management:
  endpoints:
    web:
      exposure:
        include: "*"

logging:
  level:
    com.healthdiet.gateway: debug
    org.springframework.cloud.gateway: debug
